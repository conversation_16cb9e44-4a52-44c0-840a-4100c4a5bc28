{"contractName": "UnhackableWallet", "abi": [{"inputs": [], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "reporter", "type": "address"}, {"indexed": true, "internalType": "address", "name": "suspicious<PERSON><PERSON><PERSON>", "type": "address"}, {"indexed": false, "internalType": "string", "name": "description", "type": "string"}], "name": "ScamReported", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "from", "type": "address"}, {"indexed": true, "internalType": "address", "name": "to", "type": "address"}, {"indexed": false, "internalType": "uint256", "name": "amount", "type": "uint256"}, {"indexed": false, "internalType": "bool", "name": "safe", "type": "bool"}], "name": "SecureTransfer", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "voter", "type": "address"}, {"indexed": true, "internalType": "bytes32", "name": "proposalId", "type": "bytes32"}, {"indexed": false, "internalType": "bool", "name": "inSupport", "type": "bool"}], "name": "VoteCast", "type": "event"}, {"stateMutability": "payable", "type": "fallback"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "confirmedScammers", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "daoVotes", "outputs": [{"internalType": "bool", "name": "inSupport", "type": "bool"}, {"internalType": "address", "name": "voter", "type": "address"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "index", "type": "uint256"}], "name": "getReport", "outputs": [{"internalType": "address", "name": "reporter", "type": "address"}, {"internalType": "address", "name": "suspicious<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "string", "name": "description", "type": "string"}, {"internalType": "string", "name": "evidence", "type": "string"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "votesFor", "type": "uint256"}, {"internalType": "uint256", "name": "votesAgainst", "type": "uint256"}, {"internalType": "bool", "name": "confirmed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getReportCount", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "scammer", "type": "address"}], "name": "getReports", "outputs": [{"components": [{"internalType": "address", "name": "reporter", "type": "address"}, {"internalType": "address", "name": "suspicious<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "string", "name": "reason", "type": "string"}, {"internalType": "string", "name": "evidence", "type": "string"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "votesFor", "type": "uint256"}, {"internalType": "uint256", "name": "votesAgainst", "type": "uint256"}, {"internalType": "bool", "name": "confirmed", "type": "bool"}], "internalType": "struct UnhackableWallet.Report[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "getScamScore", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "proposalId", "type": "bytes32"}], "name": "getVotes", "outputs": [{"components": [{"internalType": "bool", "name": "inSupport", "type": "bool"}, {"internalType": "address", "name": "voter", "type": "address"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}], "internalType": "struct UnhackableWallet.Vote[]", "name": "", "type": "tuple[]"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "addr", "type": "address"}], "name": "isScamAddress", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "scammer", "type": "address"}, {"internalType": "string", "name": "reason", "type": "string"}, {"internalType": "string", "name": "evidence", "type": "string"}], "name": "reportScam", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}], "name": "reports", "outputs": [{"internalType": "address", "name": "reporter", "type": "address"}, {"internalType": "address", "name": "suspicious<PERSON><PERSON><PERSON>", "type": "address"}, {"internalType": "string", "name": "reason", "type": "string"}, {"internalType": "string", "name": "evidence", "type": "string"}, {"internalType": "uint256", "name": "timestamp", "type": "uint256"}, {"internalType": "uint256", "name": "votesFor", "type": "uint256"}, {"internalType": "uint256", "name": "votesAgainst", "type": "uint256"}, {"internalType": "bool", "name": "confirmed", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}], "name": "scamScore", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address payable", "name": "to", "type": "address"}], "name": "secureTransfer", "outputs": [], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "totalReports", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "proposalId", "type": "bytes32"}, {"internalType": "bool", "name": "inSupport", "type": "bool"}], "name": "voteOnReport", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"stateMutability": "payable", "type": "receive"}], "bytecode": "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", "deployedBytecode": "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", "linkReferences": {}, "deployedLinkReferences": {}}