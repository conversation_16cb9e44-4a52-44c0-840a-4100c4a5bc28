package services

import (
	"context"
	"fmt"
	"math/big"
	"os"
	"time"

	"github.com/ethereum/go-ethereum/common"
	"github.com/ethereum/go-ethereum/crypto"
	"github.com/ethereum/go-ethereum/ethclient"
)

// BlockchainService provides utilities for interacting with blockchain
type BlockchainService struct {
	client        *ethclient.Client
	scamReportABI string // This would normally come from generated Go bindings
	networkID     *big.Int
	contractAddr  common.Address
}

// NewBlockchainService creates a new blockchain service
func NewBlockchainService() (*BlockchainService, error) {
	rpcURL := os.Getenv("ETH_RPC_URL")
	if rpcURL == "" {
		rpcURL = "http://localhost:8545" // Default to local node
	}

	client, err := ethclient.Dial(rpcURL)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to Ethereum client: %w", err)
	}

	// Get network ID
	networkID, err := client.NetworkID(context.Background())
	if err != nil {
		return nil, fmt.Errorf("failed to get network ID: %w", err)
	}

	contractAddrStr := os.Getenv("SCAM_REPORT_CONTRACT")
	if contractAddrStr == "" {
		// Default to a placeholder address for development
		contractAddrStr = "******************************************"
	}
	contractAddr := common.HexToAddress(contractAddrStr)

	return &BlockchainService{
		client:        client,
		networkID:     networkID,
		contractAddr:  contractAddr,
		scamReportABI: "", // In production this would be populated from the contract ABI
	}, nil
}

// ReportScamOnChain reports a scam address to the blockchain
func (s *BlockchainService) ReportScamOnChain(scammerAddress string, reporterAddress string, evidence string) (string, error) {
	// In a real implementation, this would call a smart contract method
	// For now we'll simulate a successful transaction
	
	// In production, you would:
	// 1. Create a transaction with the contract method call
	// 2. Sign it with the reporter's private key (or use admin key)
	// 3. Submit and wait for confirmation
	
	// Simulating a transaction hash for now
	txHash := fmt.Sprintf("0x%x", crypto.Keccak256([]byte(fmt.Sprintf("%s-%s-%d", 
		scammerAddress, reporterAddress, time.Now().UnixNano()))))
		
	return txHash, nil
}

// VerifySignature verifies that a message was signed by the given address
func (s *BlockchainService) VerifySignature(address, message, signature string) (bool, error) {
	// Convert hex signature to bytes
	sig := common.FromHex(signature)
	if len(sig) != 65 {
		return false, fmt.Errorf("invalid signature length")
	}
	
	// Ethereum signed message
	prefix := "\x19Ethereum Signed Message:\n"
	prefixedMsg := fmt.Sprintf("%s%d%s", prefix, len(message), message)
	
	// Hash the prefixed message
	hash := crypto.Keccak256Hash([]byte(prefixedMsg))
	
	// Check signature format
	if sig[64] >= 27 {
		sig[64] -= 27
	}
	
	// Recover public key
	pubKey, err := crypto.Ecrecover(hash.Bytes(), sig)
	if err != nil {
		return false, fmt.Errorf("failed to recover public key: %w", err)
	}
	
	// Convert to Ethereum address
	recoveredAddr := common.BytesToAddress(crypto.Keccak256(pubKey[1:])[12:])
	
	// Compare with the provided address
	return recoveredAddr.Hex() == address, nil
}

// TriggerAssetRecovery initiates an asset recovery process on-chain
func (s *BlockchainService) TriggerAssetRecovery(victimAddress, scammerAddress, evidence string) (string, error) {
	// This would interact with a recovery contract in production
	// For now, we'll return a simulated transaction hash
	
	txHash := fmt.Sprintf("0x%x", crypto.Keccak256([]byte(fmt.Sprintf("recovery-%s-%s-%d", 
		victimAddress, scammerAddress, time.Now().UnixNano()))))
		
	return txHash, nil
}

// GetTransactionStatus checks the status of a blockchain transaction
func (s *BlockchainService) GetTransactionStatus(txHash string) (string, error) {
	// In production, this would check the transaction receipt
	// For now, we'll simulate a completed transaction
	
	return "confirmed", nil
}

// GetWalletBalance retrieves the balance of an Ethereum address
func (s *BlockchainService) GetWalletBalance(address string) (*big.Float, error) {
	account := common.HexToAddress(address)
	balance, err := s.client.BalanceAt(context.Background(), account, nil)
	if err != nil {
		return nil, err
	}
	
	// Convert from Wei to ETH
	fbalance := new(big.Float)
	fbalance.SetString(balance.String())
	ethValue := new(big.Float).Quo(fbalance, big.NewFloat(1e18))
	
	return ethValue, nil
}
